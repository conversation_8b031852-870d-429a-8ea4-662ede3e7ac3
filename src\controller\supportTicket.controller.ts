import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { db } from "../models";
import { getPagination } from "../utils/common";
import { TICKET_STATUS } from "../helper/constant";
import {
  getTicketByIdRaw,
  getTicketBySlugRaw,
  getTicketsListRaw,
  generateTicketSlug,
} from "../helper/ticket.helper";
import organizationHelper from "../helper/organization.helper";

const Ticket = db.Ticket;

/**
 * Get all support tickets with pagination and filters (following recipe-ms pattern)
 */
const getAllTickets = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page,
      limit,
      search,
      ticket_status,
      ticket_priority,
      ticket_type,
      ticket_module,
      assigned_to_user_id,
      sort_by,
      sort_order,
    } = req.query;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId || !organizationId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Use raw query for better performance (following recipe-ms pattern)
    const { limit: queryLimit, offset } = getPagination(
      page as string,
      limit as string
    );

    const { tickets, total } = await getTicketsListRaw(organizationId, {
      limit: queryLimit,
      offset,
      search: search as string,
      ticket_status: ticket_status as string,
      ticket_priority: ticket_priority as string,
      ticket_type: ticket_type as string,
      ticket_module: ticket_module as string,
      assigned_to_user_id: assigned_to_user_id
        ? Number(assigned_to_user_id)
        : undefined,
      sort_by: sort_by as string,
      sort_order: sort_order as string,
    });

    if (!tickets || tickets.length === 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("NO_TICKETS_FOUND") || "No tickets found",
        data: [],
        ...(limit && {
          pagination: {
            total,
            page: Number(page) || 1,
            limit: Number(limit),
            totalPages: Math.ceil(total / Number(limit)),
          },
        }),
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKETS_RETRIEVED_SUCCESSFULLY") ||
        "Tickets retrieved successfully",
      data: tickets,
      ...(limit && {
        pagination: {
          total,
          page: Number(page) || 1,
          limit: Number(limit),
          totalPages: Math.ceil(total / Number(limit)),
        },
      }),
    });
  } catch (error: unknown) {
    console.error("Error in getAllTickets:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Get single ticket by ID or slug (following recipe-ms pattern)
 */
const getTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId || !organizationId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    let ticketData;
    const isNumeric = !isNaN(Number(id));

    if (isNumeric) {
      ticketData = await getTicketByIdRaw(Number(id), organizationId);
    } else {
      ticketData = await getTicketBySlugRaw(id, organizationId);
    }

    if (!ticketData) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_RETRIEVED_SUCCESSFULLY") ||
        "Ticket retrieved successfully",
      data: ticketData,
    });
  } catch (error: unknown) {
    console.error("Error in getTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Create new support ticket (following recipe-ms pattern)
 */
const createTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      ticket_title,
      ticket_description,
      ticket_module,
      ticket_type,
      ticket_priority,
      support_pin,
    } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId || !organizationId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    // Validate support PIN before creating ticket
    const authToken = req.headers.authorization?.replace("Bearer ", "");
    const pinValidation = await organizationHelper.validateSupportPin(
      organizationId,
      support_pin,
      authToken
    );

    if (!pinValidation.isValid) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("INVALID_SUPPORT_PIN") || "Invalid support PIN",
        error: pinValidation.error,
      });
    }

    // Generate unique ticket slug
    const ticketSlug = await generateTicketSlug(organizationId);

    const ticketData = {
      ticket_slug: ticketSlug,
      organization_id: organizationId,
      ticket_owner_user_id: userId,
      ticket_title,
      ticket_description,
      ticket_module,
      ticket_type,
      ticket_priority,
      ticket_status: TICKET_STATUS.OPEN,
      created_by_user_id: userId,
      updated_by_user_id: userId,
    };

    const ticket = await Ticket.create(ticketData);

    // Handle file uploads exactly like recipe microservice
    const uploadedAttachments: any[] = [];
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      // Handle ticket files upload
      if (files.ticketFiles && files.ticketFiles.length > 0) {
        const { TicketAttachment } = db;

        for (const file of files.ticketFiles) {
          // Determine attachment type based on MIME type
          let attachmentType = "document";
          if (file.mimetype.startsWith("image/")) {
            attachmentType = "image";
          } else if (file.mimetype.startsWith("video/")) {
            attachmentType = "video";
          } else if (file.mimetype.startsWith("audio/")) {
            attachmentType = "audio";
          }

          // Create attachment record matching the model structure
          const attachmentData = {
            ticket_id: ticket.id,
            item_id: file.item_id,
            attachment_type: attachmentType,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            created_by: userId,
          };

          const attachment = await TicketAttachment.create(attachmentData);
          uploadedAttachments.push({
            id: attachment.id,
            item_id: file.item_id,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            attachment_type: attachmentType,
          });
        }
      }
    }

    // Include attachments in response if any were uploaded
    const responseData = {
      ...ticket.toJSON(),
      attachments:
        uploadedAttachments.length > 0 ? uploadedAttachments : undefined,
    };

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message:
        res.__("TICKET_CREATED_SUCCESSFULLY") || "Ticket created successfully",
      data: responseData,
    });
  } catch (error: unknown) {
    console.error("Error in createTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Update support ticket (following recipe-ms pattern)
 */
const updateTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId || !organizationId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const ticket = await Ticket.findOne({
      where: {
        id: Number(id),
        organization_id: organizationId,
        deleted_at: null,
      },
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Add audit fields
    updateData.updated_by_user_id = userId;

    await ticket.update(updateData);

    const uploadedAttachments: any[] = [];
    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      // Handle ticket files upload
      if (files.ticketFiles && files.ticketFiles.length > 0) {
        const { TicketAttachment } = db;

        for (const file of files.ticketFiles) {
          // Determine attachment type based on MIME type
          let attachmentType = "document";
          if (file.mimetype.startsWith("image/")) {
            attachmentType = "image";
          } else if (file.mimetype.startsWith("video/")) {
            attachmentType = "video";
          } else if (file.mimetype.startsWith("audio/")) {
            attachmentType = "audio";
          }

          // Create attachment record matching the model structure
          const attachmentData = {
            ticket_id: ticket.id,
            item_id: file.item_id,
            attachment_type: attachmentType,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            created_by: userId,
          };

          const attachment = await TicketAttachment.create(attachmentData);
          uploadedAttachments.push({
            id: attachment.id,
            item_id: file.item_id,
            attachment_name: file.originalname,
            file_size: file.size,
            mime_type: file.mimetype,
            attachment_type: attachmentType,
          });
        }
      }
    }

    // Include attachments in response if any were uploaded
    const responseData = {
      ...ticket.toJSON(),
      attachments:
        uploadedAttachments.length > 0 ? uploadedAttachments : undefined,
    };

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_UPDATED_SUCCESSFULLY") || "Ticket updated successfully",
      data: responseData,
    });
  } catch (error: unknown) {
    console.error("Error in updateTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Delete support ticket (following recipe-ms pattern)
 */
const deleteTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId || !organizationId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const ticket = await Ticket.findOne({
      where: {
        id: Number(id),
        organization_id: organizationId,
        deleted_at: null,
      },
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    // Soft delete
    await ticket.update({
      deleted_at: new Date(),
      updated_by_user_id: userId,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_DELETED_SUCCESSFULLY") || "Ticket deleted successfully",
    });
  } catch (error: unknown) {
    console.error("Error in deleteTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Assign ticket to user (following recipe-ms pattern)
 */
const assignTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { assigned_to_user_id } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId || !organizationId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const ticket = await Ticket.findOne({
      where: {
        id: Number(id),
        organization_id: organizationId,
        deleted_at: null,
      },
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    await ticket.update({
      assigned_to_user_id: assigned_to_user_id,
      assigned_at: new Date(),
      assigned_by_user_id: userId,
      ticket_status: TICKET_STATUS.ASSIGNED,
      updated_by_user_id: userId,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_ASSIGNED_SUCCESSFULLY") ||
        "Ticket assigned successfully",
      data: ticket,
    });
  } catch (error: unknown) {
    console.error("Error in assignTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Resolve ticket (following recipe-ms pattern)
 */
const resolveTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { resolution_note } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId || !organizationId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const ticket = await Ticket.findOne({
      where: {
        id: Number(id),
        organization_id: organizationId,
        deleted_at: null,
      },
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    await ticket.update({
      ticket_status: TICKET_STATUS.RESOLVED,
      resolution_note,
      resolved_at: new Date(),
      resolved_by_user_id: userId,
      updated_by_user_id: userId,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_RESOLVED_SUCCESSFULLY") ||
        "Ticket resolved successfully",
      data: ticket,
    });
  } catch (error: unknown) {
    console.error("Error in resolveTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * Rate ticket (following recipe-ms pattern)
 */
const rateTicket = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { rating, review_comment } = req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId || !organizationId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS") || "Unauthorized access",
      });
    }

    const ticket = await Ticket.findOne({
      where: {
        id: Number(id),
        organization_id: organizationId,
        deleted_at: null,
      },
    });

    if (!ticket) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("TICKET_NOT_FOUND") || "Ticket not found",
      });
    }

    await ticket.update({
      rating,
      review_comment,
      rated_at: new Date(),
      rated_by_user_id: userId,
      updated_by_user_id: userId,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message:
        res.__("TICKET_RATED_SUCCESSFULLY") || "Ticket rated successfully",
      data: ticket,
    });
  } catch (error: unknown) {
    console.error("Error in rateTicket:", error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("INTERNAL_SERVER_ERROR") || "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Export all controller functions (following recipe-ms pattern)
export default {
  getAllTickets,
  getTicket,
  createTicket,
  updateTicket,
  deleteTicket,
  assignTicket,
  resolveTicket,
  rateTicket,
};
