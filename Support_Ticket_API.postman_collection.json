{"info": {"_postman_id": "support-ticket-ms-complete-api-v2", "name": "🎫 Support Ticket API's", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🏥 Health & Status", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Check if the support ticket microservice is running and get available routes"}, "response": []}]}, {"name": "🎫 Support Tickets", "item": [{"name": "Get All Tickets (with filters)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/list?page=1&limit=10&organization_id={{organization_id}}&ticket_status=open&ticket_priority=high&ticket_module=hrms&ticket_type=bug&assigned_to_user_id=123&search=test&sort_by=created_at&sort_order=DESC", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "list"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization ID"}, {"key": "ticket_status", "value": "open", "description": "Filter by status: open, assigned, in_progress, on_hold, escalated, qa_review, under_review, resolved, closed"}, {"key": "ticket_priority", "value": "high", "description": "Filter by priority: low, medium, high, urgent"}, {"key": "ticket_module", "value": "hrms", "description": "Filter by module: hrms, pms, other"}, {"key": "ticket_type", "value": "bug", "description": "Filter by type: bug, request_for_feature, general_query, technical, non_technical, export_help, support"}, {"key": "assigned_to_user_id", "value": "123", "description": "Filter by assigned user ID"}, {"key": "search", "value": "test", "description": "Search in ticket title and description"}, {"key": "sort_by", "value": "created_at", "description": "Sort by field"}, {"key": "sort_order", "value": "DESC", "description": "Sort order: ASC, DESC"}]}, "description": "Get all support tickets with comprehensive filtering and pagination options"}, "response": []}, {"name": "Get Single Ticket", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "{{ticket_id}}"]}, "description": "Get a single ticket by ID"}, "response": []}, {"name": "Create Ticket (with file upload)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ticket_title", "value": "Unable to export recipe data", "description": "Required: 5-200 characters", "type": "text"}, {"key": "ticket_description", "value": "The export button in the recipe module is not responding when clicked. This is affecting our daily operations as we cannot export recipe data for our reports.", "description": "Required: 10-5000 characters", "type": "text"}, {"key": "ticket_module", "value": "hrms", "description": "Optional: hrms, pms, other (default: other)", "type": "text"}, {"key": "ticket_type", "value": "bug", "description": "Optional: bug, request_for_feature, general_query, technical, non_technical, export_help, support (default: general_query)", "type": "text"}, {"key": "ticket_priority", "value": "high", "description": "Optional: low, medium, high, urgent (default: medium)", "type": "text"}, {"key": "support_pin", "value": "{{support_pin}}", "description": "Required: 1-50 characters. Organization support PIN for validation", "type": "text"}, {"key": "ticketFiles", "description": "Optional: Upload up to 5 files, max 10 MB each", "type": "file", "disabled": true}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/create", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "create"]}, "description": "Create a new support ticket with optional file attachments. Support PIN is required for validation."}, "response": []}, {"name": "Update Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ticket_title", "value": "Updated: Unable to export recipe data", "description": "Optional: 5-200 characters", "type": "text"}, {"key": "ticket_description", "value": "Updated description with more details about the export issue", "description": "Optional: 10-5000 characters", "type": "text"}, {"key": "ticket_module", "value": "hrms", "description": "Optional: hrms, pms, other", "type": "text"}, {"key": "ticket_type", "value": "bug", "description": "Optional: bug, request_for_feature, general_query, technical, non_technical, export_help, support", "type": "text"}, {"key": "ticket_priority", "value": "urgent", "description": "Optional: low, medium, high, urgent", "type": "text"}, {"key": "ticket_status", "value": "in_progress", "description": "Optional: open, assigned, in_progress, on_hold, escalated, qa_review, under_review, resolved, closed", "type": "text"}, {"key": "assigned_to_user_id", "value": "456", "description": "Optional: User ID to assign ticket to (positive integer)", "type": "text"}, {"key": "resolution_note", "value": "Working on the export functionality fix. This issue has been identified and a patch is being developed.", "description": "Optional: Resolution note (10-2000 characters)", "type": "text"}, {"key": "rating", "value": "4", "description": "Optional: Rating from 1-5 (integer)", "type": "text"}, {"key": "review_comment", "value": "Good response time and helpful support", "description": "Optional: Review comment (max 1000 characters)", "type": "text"}, {"key": "ticketFiles", "description": "Optional: Upload up to 5 files, max 50MB each", "type": "file", "disabled": true}]}, "url": {"raw": "{{base_url}}/v1/private/tickets/update/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "update", "{{ticket_id}}"]}, "description": "Update an existing support ticket with optional file attachments"}, "response": []}, {"name": "Delete Ticket", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/tickets/delete/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "delete", "{{ticket_id}}"]}, "description": "Delete a support ticket"}, "response": []}, {"name": "Assign <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"assigned_to_user_id\": 456\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/assign/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "assign", "{{ticket_id}}"]}, "description": "Assign a ticket to a user"}, "response": []}, {"name": "Resolve Ticket", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"resolution_note\": \"The export functionality has been fixed. The issue was caused by a missing database index which has now been added. Please try the export feature again.\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/resolve/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "resolve", "{{ticket_id}}"]}, "description": "Resolve a ticket with resolution note (10-2000 characters required)"}, "response": []}, {"name": "Rate Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 5,\n  \"review_comment\": \"Excellent support! The issue was resolved quickly and the explanation was clear.\"\n}"}, "url": {"raw": "{{base_url}}/v1/private/tickets/rate/{{ticket_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "tickets", "rate", "{{ticket_id}}"]}, "description": "Rate a ticket (1-5 stars required, review comment optional, max 1000 characters)"}, "response": []}]}, {"name": "💬 Support Messages", "item": [{"name": "Get Ticket Messages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/messages/tickets/{{ticket_id}}/messages?page=1&limit=50&include_private=false&message_type=USER&since=2024-01-01T00:00:00Z", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "tickets", "{{ticket_id}}", "messages"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "50", "description": "Messages per page (1-100, default: 50)"}, {"key": "include_private", "value": "false", "description": "Include private/internal messages"}, {"key": "message_type", "value": "USER", "description": "Filter by message type: USER, AGENT, SYSTEM, INTERNAL_NOTE"}, {"key": "since", "value": "2024-01-01T00:00:00Z", "description": "Get messages since this timestamp"}]}, "description": "Get all messages for a specific ticket with filtering options"}, "response": []}, {"name": "Add Message to Ticket", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "message_text", "value": "Thank you for reporting this issue. We are investigating the export functionality problem and will update you shortly.", "description": "Required: 1-2000 characters", "type": "text"}, {"key": "message_type", "value": "AGENT", "description": "Optional: USER, AGENT, SYSTEM, INTERNAL_NOTE (default: USER)", "type": "text"}, {"key": "is_private", "value": "false", "description": "Optional: Whether message is private (default: false)", "type": "text"}, {"key": "messageFiles", "description": "Optional: Upload up to 10 files, max 50MB each", "type": "file", "disabled": true}]}, "url": {"raw": "{{base_url}}/v1/private/messages/tickets/{{ticket_id}}/messages", "host": ["{{base_url}}"], "path": ["v1", "private", "messages", "tickets", "{{ticket_id}}", "messages"]}, "description": "Add a new message to a support ticket with optional file attachments"}, "response": []}]}, {"name": "⚙️ Support Configuration", "item": [{"name": "Get All Configurations (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/config?page=1&limit=10&is_active=true&organization_id={{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "config"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "is_active", "value": "true", "description": "Filter by active status"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization ID"}]}, "description": "Get all support configurations (Admin only)"}, "response": []}, {"name": "Get Default Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/config/default", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "config", "default"]}, "description": "Get the default configuration that applies to all organizations"}, "response": []}, {"name": "Update Default Configuration", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"is_active\": true,\n  \"allow_attachments\": true,\n  \"max_attachment_size\": 5242880,\n  \"allowed_file_types\": [\"pdf\", \"png\", \"jpg\", \"jpeg\", \"doc\", \"docx\"],\n  \"auto_assignment_enabled\": false,\n  \"sla_response_time_hours\": 24,\n  \"sla_resolution_time_hours\": 72\n}"}, "url": {"raw": "{{base_url}}/v1/private/config/config/default", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "config", "default"]}, "description": "Create or update the default configuration for all organizations"}, "response": []}, {"name": "Get Organization Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "config", "{{organization_id}}"]}, "description": "Get support configuration for a specific organization"}, "response": []}, {"name": "Create/Update Organization Configuration", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"support_pin\": \"{{support_pin}}\",\n  \"is_active\": true,\n  \"allow_attachments\": true,\n  \"max_attachment_size\": 5242880,\n  \"allowed_file_types\": [\"pdf\", \"png\", \"jpg\", \"jpeg\", \"doc\", \"docx\", \"txt\", \"rtf\", \"xls\", \"xlsx\", \"csv\", \"zip\", \"rar\", \"7z\"],\n  \"auto_assignment_enabled\": false,\n  \"sla_response_time_hours\": 24,\n  \"sla_resolution_time_hours\": 72\n}"}, "url": {"raw": "{{base_url}}/v1/private/config/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "config", "{{organization_id}}"]}, "description": "Create or update support configuration for an organization"}, "response": []}, {"name": "Delete Organization Configuration", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/config/config/{{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "config", "{{organization_id}}"]}, "description": "Delete support configuration for an organization"}, "response": []}, {"name": "Toggle Support Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"is_active\": false\n}"}, "url": {"raw": "{{base_url}}/v1/private/config/config/{{organization_id}}/toggle", "host": ["{{base_url}}"], "path": ["v1", "private", "config", "config", "{{organization_id}}", "toggle"]}, "description": "Enable or disable support for an organization"}, "response": []}]}, {"name": "👨‍💼 Admin Operations", "item": [{"name": "Admin Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/dashboard?organization_id={{organization_id}}", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "dashboard"], "query": [{"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)"}]}, "description": "Get comprehensive dashboard data including statistics, metrics, and quick actions"}, "response": []}, {"name": "Get Tickets with Advanced Filters (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/tickets?page=1&limit=10&search=export&status=open&priority=high&module_type=hrms&issue_type=bug&assigned_to_user_id=null&organization_id={{organization_id}}&submitter_email=<EMAIL>&date_from=2024-01-01&date_to=2024-12-31&overdue=true&unassigned=true&has_rating=false&sort_by=created_at&sort_order=DESC", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (1-100, default: 10)"}, {"key": "search", "value": "export", "description": "Search in ticket number, subject, submitter name/email"}, {"key": "ticket_status", "value": "open", "description": "Filter by status: open, assigned, in_progress, on_hold, qa_review, under_review, escalated, resolved, closed"}, {"key": "ticket_priority", "value": "high", "description": "Filter by priority: low, medium, high, urgent"}, {"key": "ticket_module", "value": "hrms", "description": "Filter by module type: hrms, pms, other"}, {"key": "ticket_type", "value": "bug", "description": "Filter by issue type: bug, request_for_feature, general_query, technical, non_technical, export_help, support"}, {"key": "assigned_to_user_id", "value": "null", "description": "Filter by assigned user (use 'null' for unassigned)"}, {"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)"}, {"key": "submitter_email", "value": "<EMAIL>", "description": "Filter by submitter email"}, {"key": "date_from", "value": "2024-01-01", "description": "Filter tickets created from this date"}, {"key": "date_to", "value": "2024-12-31", "description": "Filter tickets created until this date"}, {"key": "overdue", "value": "true", "description": "Filter overdue tickets only"}, {"key": "unassigned", "value": "true", "description": "Filter unassigned tickets only"}, {"key": "has_rating", "value": "false", "description": "Filter tickets with/without rating"}, {"key": "sort_by", "value": "created_at", "description": "Sort field"}, {"key": "sort_order", "value": "DESC", "description": "Sort order: ASC, DESC"}]}, "description": "Retrieve tickets with comprehensive filtering and search capabilities for admin users"}, "response": []}, {"name": "Bulk Ticket Operations", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"ticket_ids\": [1, 2, 3, 4, 5],\n  \"operation\": \"assign\",\n  \"data\": {\n    \"assigned_to_user_id\": 123,\n    \"change_note\": \"Bulk assignment to support team lead\"\n  }\n}"}, "url": {"raw": "{{base_url}}/v1/private/admin/tickets/bulk", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "tickets", "bulk"]}, "description": "Execute bulk operations on multiple tickets: assign, status_update, priority_update, delete"}, "response": []}, {"name": "Get Analytics and Reports", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/v1/private/admin/analytics?organization_id={{organization_id}}&period=30d&date_from=2024-01-01&date_to=2024-12-31", "host": ["{{base_url}}"], "path": ["v1", "private", "admin", "analytics"], "query": [{"key": "organization_id", "value": "{{organization_id}}", "description": "Filter by organization (admin only)"}, {"key": "period", "value": "30d", "description": "Time period: 7d, 30d, 90d, 1y (default: 30d)"}, {"key": "date_from", "value": "2024-01-01", "description": "Custom start date (overrides period)"}, {"key": "date_to", "value": "2024-12-31", "description": "Custom end date (overrides period)"}]}, "description": "Get comprehensive analytics including ticket trends, performance metrics, SLA compliance, and customer satisfaction"}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://localhost:5008", "type": "string", "description": "Base URL for the Support Ticket Microservice"}, {"key": "auth_token", "value": "your-jwt-token-here", "type": "string", "description": "JWT authentication token for API access"}, {"key": "organization_id", "value": "your-organization-id", "type": "string", "description": "Organization ID for filtering and access control"}, {"key": "ticket_id", "value": "1", "type": "string", "description": "Ticket ID for specific ticket operations"}, {"key": "support_pin", "value": "1234", "type": "string", "description": "Support PIN for ticket creation (1-50 characters)"}]}