import express from "express";
import adminController from "../../controller/admin.controller";
import { adminAuth, agentAuth } from "../../middleware/adminAuth";
import {
  bulkTicketOperationValidation,
  ticketAnalyticsValidation,
  dashboardValidation,
  getTicketsWithFiltersValidation,
  agentPerformanceValidation,
  exportTicketsValidation,
} from "../../validators/admin.validator";

const router = express.Router();

/**
 * @swagger
 * /api/private/admin/dashboard:
 *   get:
 *     tags:
 *       - Admin Dashboard
 *     summary: Get admin dashboard overview
 *     description: Get comprehensive dashboard data including statistics, metrics, and quick actions
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *         description: Filter by organization (admin only)
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     overview:
 *                       type: object
 *                       properties:
 *                         total_tickets:
 *                           type: integer
 *                           example: 150
 *                         unassigned_tickets:
 *                           type: integer
 *                           example: 12
 *                         overdue_tickets:
 *                           type: integer
 *                           example: 5
 *                         avg_response_time_hours:
 *                           type: number
 *                           example: 2.5
 *                     statistics:
 *                       type: object
 *                       properties:
 *                         by_status:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               ticket_status:
 *                                 type: string
 *                               count:
 *                                 type: integer
 *                         by_priority:
 *                           type: array
 *                           items:
 *                             type: object
 *                         by_module:
 *                           type: array
 *                           items:
 *                             type: object
 *                     recent_activity:
 *                       type: array
 *                       items:
 *                         type: object
 *                     quick_actions:
 *                       type: array
 *                       items:
 *                         type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Server error
 */
router.get("/dashboard", agentAuth, adminController.getDashboard);

/**
 * @swagger
 * /api/private/admin/tickets:
 *   get:
 *     tags:
 *       - Admin Ticket Management
 *     summary: Get tickets with advanced filtering
 *     description: Retrieve tickets with comprehensive filtering and search capabilities
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in ticket number, subject, submitter name/email
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [OPEN, ASSIGNED, IN_PROGRESS, ON_HOLD, QA_REVIEW, UNDER_REVIEW, ESCALATED, RESOLVED, CLOSED, INVOICED]
 *         description: Filter by ticket status (can be array)
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [EMERGENCY, URGENT, HIGH, MEDIUM, LOW]
 *         description: Filter by priority (can be array)
 *       - in: query
 *         name: module_type
 *         schema:
 *           type: string
 *           enum: [HRMS, PMS, OTHER]
 *         description: Filter by module type
 *       - in: query
 *         name: issue_type
 *         schema:
 *           type: string
 *           enum: [BUG, FEATURE_REQUEST, QUERY, EXPORT_ISSUE, TECHNICAL_SUPPORT, OTHER]
 *         description: Filter by issue type
 *       - in: query
 *         name: assigned_to_user_id
 *         schema:
 *           type: string
 *         description: Filter by assigned user (use 'null' for unassigned)
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *         description: Filter by organization (admin only)
 *       - in: query
 *         name: submitter_email
 *         schema:
 *           type: string
 *         description: Filter by submitter email
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter tickets created from this date
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter tickets created until this date
 *       - in: query
 *         name: overdue
 *         schema:
 *           type: boolean
 *         description: Filter overdue tickets only
 *       - in: query
 *         name: unassigned
 *         schema:
 *           type: boolean
 *         description: Filter unassigned tickets only
 *       - in: query
 *         name: has_rating
 *         schema:
 *           type: boolean
 *         description: Filter tickets with/without rating
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           default: created_at
 *         description: Sort field
 *       - in: query
 *         name: sort_order
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: DESC
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Tickets retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  "/tickets",
  agentAuth,
  getTicketsWithFiltersValidation(),
  adminController.getTicketsWithFilters
);

/**
 * @swagger
 * /api/private/admin/tickets/bulk:
 *   post:
 *     tags:
 *       - Admin Ticket Management
 *     summary: Perform bulk operations on tickets
 *     description: Execute bulk operations like assign, status update, priority update, or delete on multiple tickets
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ticket_ids
 *               - operation
 *             properties:
 *               ticket_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 minItems: 1
 *                 description: Array of ticket IDs to operate on
 *               operation:
 *                 type: string
 *                 enum: [assign, status_update, priority_update, delete]
 *                 description: Type of bulk operation
 *               data:
 *                 type: object
 *                 description: Operation-specific data
 *                 oneOf:
 *                   - type: object
 *                     properties:
 *                       assigned_to_user_id:
 *                         type: integer
 *                       change_note:
 *                         type: string
 *                   - type: object
 *                     properties:
 *                       status:
 *                         type: string
 *                         enum: [OPEN, ASSIGNED, IN_PROGRESS, ON_HOLD, QA_REVIEW, UNDER_REVIEW, ESCALATED, RESOLVED, CLOSED, INVOICED]
 *                       change_note:
 *                         type: string
 *                   - type: object
 *                     properties:
 *                       priority:
 *                         type: string
 *                         enum: [EMERGENCY, URGENT, HIGH, MEDIUM, LOW]
 *                       change_note:
 *                         type: string
 *     responses:
 *       200:
 *         description: Bulk operation completed
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Server error
 */
router.post(
  "/tickets/bulk",
  adminAuth,
  bulkTicketOperationValidation(),
  adminController.bulkTicketOperation
);

/**
 * @swagger
 * /api/private/admin/analytics:
 *   get:
 *     tags:
 *       - Admin Analytics
 *     summary: Get comprehensive ticket analytics and reports
 *     description: Retrieve detailed analytics including ticket trends, performance metrics, SLA compliance, and customer satisfaction
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organization_id
 *         schema:
 *           type: string
 *         description: Filter by organization (admin only)
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y]
 *           default: 30d
 *         description: Time period for analytics
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Custom start date (overrides period)
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Custom end date (overrides period)
 *     responses:
 *       200:
 *         description: Analytics data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     overview:
 *                       type: object
 *                       properties:
 *                         total_tickets:
 *                           type: integer
 *                         avg_response_time_hours:
 *                           type: number
 *                         avg_resolution_time_hours:
 *                           type: number
 *                         sla_compliance_rate:
 *                           type: number
 *                         customer_satisfaction:
 *                           type: number
 *                     trends:
 *                       type: object
 *                       properties:
 *                         ticket_volume:
 *                           type: array
 *                           items:
 *                             type: object
 *                     distributions:
 *                       type: object
 *                       properties:
 *                         by_status:
 *                           type: array
 *                         by_priority:
 *                           type: array
 *                         by_module:
 *                           type: array
 *                         by_issue_type:
 *                           type: array
 *                     performance:
 *                       type: object
 *                       properties:
 *                         response_time:
 *                           type: object
 *                         resolution_time:
 *                           type: object
 *                         agent_performance:
 *                           type: array
 *                         sla_metrics:
 *                           type: object
 *                         satisfaction:
 *                           type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Server error
 */
router.get(
  "/analytics",
  adminAuth,
  ticketAnalyticsValidation(),
  adminController.getAnalytics
);

export default router;
