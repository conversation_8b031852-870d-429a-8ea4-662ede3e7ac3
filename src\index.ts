import express, { Express } from "express";
import dotenv from "dotenv";
import http from "http";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import rateLimit from "express-rate-limit";
import { errors } from "celebrate";
import morgan from "morgan";
import cookieParser from "cookie-parser";
import swaggerUi from "swagger-ui-express";
import { swaggerSpec } from "./swagger/swagger.config";

// Import utilities and helpers
import i18n from "./utils/i18n";
import { ResponseHelper } from "./helper/response.helper";
import { logger } from "./utils/logger";
// DatabaseHelper will be imported after global config is set

// Load environment variables
dotenv.config();
const env = process.env.NEXT_NODE_ENV || "development";

/*
  Inherit shared config & db config just like other micro-services.
  We rely on global.config and global.db populated from shared/config/**.json files.
*/
import config from "../shared/config/config.json";
import dbconfig from "../shared/config/db.json";

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
global.config = JSON.parse(JSON.stringify(config))[env];
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
global.db = JSON.parse(JSON.stringify(dbconfig))[env];

// Environment and database configuration loaded
console.log(`🔧 Environment: ${env}`);

// Import models and helpers after global config is set (following recipe-ms pattern)
import { db } from "./models";
import { DatabaseHelper } from "./helper/database.helper";

// Import routes and middleware after global config is set (like recipe and auth microservices)
import { privateRoutes, publicRoutes } from "./routes";
import userAuth from "./middleware/auth";

// Import RabbitMQ setup (like recipe and auth microservices)
import { setupConsumers } from "./rabbitmq/consumerQueue";

// Create Express app and HTTP server
const app: Express = express();
const router = express.Router();
const server = http.createServer(app);

// Trust proxy for rate limiting
app.set("trust proxy", 1);

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  })
);

// CORS configuration
app.use(
  cors({
    origin: global.config.CORS_ORIGIN || "*",
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
    credentials: true,
  })
);

// Compression and logging
app.use(compression());
app.use(morgan("combined"));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    status: false,
    message: "Too many requests from this IP, please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Body parsing middleware
router.use(
  express.json({
    limit: "10mb",
    verify: (req: any, _res: any, buf: any) => {
      req.rawBody = buf;
    },
  })
);
router.use(express.urlencoded({ extended: true, limit: "10mb" }));
router.use(cookieParser());

// Internationalization
router.use(i18n.init);

// Request logging middleware with structured logging
app.use((req, res, next) => {
  const start = Date.now();

  res.on("finish", () => {
    const duration = Date.now() - start;
    logger.request(req.method, req.originalUrl, res.statusCode, duration, {
      ip: req.ip,
      userAgent: req.get("User-Agent"),
      userId: (req as any).user?.id,
      organizationId: (req as any).user?.organization_id,
    });
  });

  next();
});

// API Routes ()
router.all("/v1/private/*", userAuth);
router.use("/v1/private", privateRoutes);
router.use("/v1/public", publicRoutes); // Public routes exist but only return auth required messages

app.use(router);

function getAllRoutes(app: any) {
  const routes: any = [];

  function formatRoute(path: string) {
    const route = path
      // eslint-disable-next-line no-useless-escape
      .replace(/^\^\\\/?\(\?\=\\\/\|\$\)\^/, "") // Remove leading regex patterns
      // eslint-disable-next-line no-useless-escape
      .replace(/\\\/\?\(\?\=\\\/\|\$\)/g, "") // Remove optional trailing slash regex
      .replace(/\^|\$|\\/g, "") // Remove remaining ^, $, and \
      .replace("(?:/([/]+?))/?", "/:id");
    return route;
  }

  function processStack(stack: any, basePath = "") {
    stack.forEach((layer: any) => {
      if (layer.route) {
        // If it's a direct route
        Object.keys(layer.route.methods).forEach((method) => {
          if (["GET", "POST", "PUT", "DELETE"].includes(method.toUpperCase())) {
            routes.push({
              method: method.toUpperCase(),
              path: formatRoute(basePath + layer.regexp.source),
              params: (layer.route.path.match(/:\w+/g) || []).map(
                (param: string) => param.replace(":", "")
              ),
            });
          }
        });
      } else if (layer.name === "router" && layer.handle.stack) {
        // If it's a router, recurse
        processStack(layer.handle.stack, basePath + layer.regexp.source);
      }
    });
  }

  processStack(app._router.stack);
  return routes;
}

app.get("/", (_req: any, res: any) => {
  const json: any = {
    message: "Welcome to Customer Service Microservice",
    data: getAllRoutes(app),
  };

  return res.send(json);
});

// Swagger documentation
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// Celebrate validation error handler
app.use(errors());

// Global error handling middleware
app.use(
  (
    err: any,
    _req: express.Request,
    res: express.Response,
    _next: express.NextFunction
  ): any => {
    logger.error("Global error handler", err, {
      url: _req.url,
      method: _req.method,
      ip: _req.ip,
      userAgent: _req.get("User-Agent"),
    });

    // Handle different types of errors
    if (err.name === "SequelizeValidationError") {
      return ResponseHelper.validationError(
        res,
        err.errors.map((e: any) => e.message)
      );
    }

    if (err.name === "SequelizeUniqueConstraintError") {
      return ResponseHelper.conflict(res, "DUPLICATE_ENTRY");
    }

    if (err.name === "SequelizeForeignKeyConstraintError") {
      return ResponseHelper.validationError(res, [
        "Invalid reference to related record",
      ]);
    }

    if (err.name === "SequelizeTimeoutError") {
      return ResponseHelper.serviceUnavailable(res, "DATABASE_TIMEOUT");
    }

    if (err.status === 413) {
      return ResponseHelper.validationError(res, ["Request payload too large"]);
    }

    if (err.type === "entity.parse.failed") {
      return ResponseHelper.validationError(res, ["Invalid JSON format"]);
    }

    // Default error response
    return ResponseHelper.error(
      res,
      "INTERNAL_SERVER_ERROR",
      process.env.NODE_ENV === "development" ? err : undefined,
      err.status || 500
    );
  }
);

// 404 handler
app.use("*", (_req: express.Request, res: express.Response) => {
  ResponseHelper.notFound(res, "ROUTE_NOT_FOUND");
});

// Initialize application
const initializeApp = async () => {
  try {
    // Show beautiful banner
    logger.banner();

    logger.startup("Initializing Customer Service Microservice");

    // Initialize database (following recipe-ms pattern)
    await db.sequelize.sync({ alter: true });
    logger.success("Database connected and synchronized");

    // Setup RabbitMQ consumers (like recipe and auth microservices)
    // await setupConsumers();
    // logger.success("RabbitMQ consumers initialized");

    // Start server
    const PORT = global.config.PORT || process.env.CUSTOMER_MS_PORT || 5008;
    server.listen(PORT, () => {
      logger.success("Customer Service Microservice started", {
        port: PORT,
        environment: env,
        apiDocs: `http://localhost:${PORT}/api-docs`,
        healthCheck: `http://localhost:${PORT}/`,
      });
    });

    // Graceful shutdown handling
    const gracefulShutdown = async (signal: string) => {
      logger.warn(`Received ${signal}. Starting graceful shutdown...`);

      try {
        // Close HTTP server
        server.close(() => {
          logger.success("HTTP server closed");
        });

        // Close database connection
        await db.sequelize.close();
        logger.success("Database connection closed");

        logger.success("Graceful shutdown completed");
        process.exit(0);
      } catch (error) {
        logger.error("Error during graceful shutdown", error);
        process.exit(1);
      }
    };

    // // Handle shutdown signals
    // process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    // process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    // // Handle uncaught exceptions
    // process.on("uncaughtException", (error) => {
    //   logger.error("Uncaught Exception", error);
    //   gracefulShutdown("UNCAUGHT_EXCEPTION");
    // });

    // // Handle unhandled promise rejections
    // process.on("unhandledRejection", (reason, promise) => {
    //   logger.error("Unhandled Rejection", reason, { promise });
    //   gracefulShutdown("UNHANDLED_REJECTION");
    // });
  } catch (error) {
    logger.error("Initialization error", error);
    // process.exit(1);
  }
};

// Start the application
initializeApp();

export { app, server };
