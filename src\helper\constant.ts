/**
 * Constants for Customer Service / Support Ticket Microservice
 * Following TTH project standards with proper enum definitions and configurations.
 * Based on recipe-ms and auth-ms patterns for consistency across microservices.
 */

// ===== ROLE CONSTANTS =====
// Following exact pattern from recipe-ms and auth-ms

export const ROLE_CONSTANT = Object.freeze({
  SUPER_ADMIN: "Super Admin",
  ADMIN: "Admin",
  DIRECTOR: "Director",
  HR: "HR",
  AREA_MANAGER: "Area Manager",
  ACCOUNTANT: "Accountant",
  BRANCH_MANAGER: "Branch Manager",
  ASSIGN_BRANCH_MANAGER: "Assist. Branch Manager",
  HEAD_CHEF: "Head Chef",
  BAR_MANAGER: "Bar Manager",
  FOH: "FOH",
  BAR: "Bar",
  KITCHEN: "Kitchen",
  HOTEL_MANAGER: "Hotel Manager",
  ASSIGN_HOTEL_MANAGER: "Assist. Hotel Manager",
  RECEPTIONIST: "Receptionist",
  HEAD_HOUSEKEEPER: "Head Housekeeper",
  HOUSE_KEEPER: "House Keeper",
  <PERSON>IG<PERSON><PERSON><PERSON>: "Signature",
});

export const ADMIN_SIDE_USER = Object.freeze([
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.DIRECTOR,
  ROLE_CONSTANT.ACCOUNTANT,
  ROLE_CONSTANT.HR,
  ROLE_CONSTANT.AREA_MANAGER,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.SIGNATURE,
]);

export const NORMAL_USER = Object.freeze([
  ROLE_CONSTANT.SUPER_ADMIN,
  ROLE_CONSTANT.ADMIN,
  ROLE_CONSTANT.BRANCH_MANAGER,
  ROLE_CONSTANT.ASSIGN_BRANCH_MANAGER,
  ROLE_CONSTANT.HEAD_CHEF,
  ROLE_CONSTANT.BAR_MANAGER,
  ROLE_CONSTANT.FOH,
  ROLE_CONSTANT.BAR,
  ROLE_CONSTANT.KITCHEN,
  ROLE_CONSTANT.HOTEL_MANAGER,
  ROLE_CONSTANT.ASSIGN_HOTEL_MANAGER,
  ROLE_CONSTANT.RECEPTIONIST,
  ROLE_CONSTANT.HEAD_HOUSEKEEPER,
  ROLE_CONSTANT.HOUSE_KEEPER,
]);

// ===== MESSAGE TYPE CONSTANTS =====
// Centralized message type enum for consistency

export const MESSAGE_TYPE = Object.freeze({
  USER: "USER",
  AGENT: "AGENT",
  SYSTEM: "SYSTEM",
  INTERNAL_NOTE: "INTERNAL_NOTE",
});

// ===== TICKET SYSTEM CONSTANTS =====
// Following recipe-ms enum pattern with different keys and values

/**
 * Ticket Status - Lifecycle states of a support ticket
 * Following recipe-ms enum pattern with different keys and values as per standards
 */
export const TICKET_STATUS = Object.freeze({
  OPEN: "open",
  ASSIGNED: "assigned",
  IN_PROGRESS: "in_progress",
  ON_HOLD: "on_hold",
  ESCALATED: "escalated",
  QA_REVIEW: "qa_review",
  UNDER_REVIEW: "under_review",
  RESOLVED: "resolved",
  CLOSED: "closed",
});

/**
 * Priority Levels - Based on TTH requirements document
 * Following recipe-ms enum pattern with different keys and values, removed NONE and EMERGENCY as per complaints
 */
export const TICKET_PRIORITY = Object.freeze({
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high",
  URGENT: "urgent",
});

/**
 * Module Types - Based on TTH requirements document
 * Following recipe-ms enum pattern with different keys and values
 */
export const TICKET_MODULE = Object.freeze({
  HRMS: "hrms",
  PMS: "pms",
  OTHER: "other",
});

/**
 * Issue Types - Based on TTH requirements document
 * Following recipe-ms enum pattern with different keys and values
 */
export const TICKET_TYPE = Object.freeze({
  BUG: "bug",
  REQUEST_FOR_FEATURE: "request_for_feature",
  GENERAL_QUERY: "general_query",
  TECHNICAL: "technical",
  NON_TECHNICAL: "non_technical",
  EXPORT_HELP: "export_help",
  SUPPORT: "support",
});

// ===== ACTION TYPE CONSTANTS =====
// For ticket history tracking

export const ACTION_TYPE = Object.freeze({
  CREATED: "CREATED",
  STATUS_CHANGED: "STATUS_CHANGED",
  PRIORITY_CHANGED: "PRIORITY_CHANGED",
  ASSIGNED: "ASSIGNED",
  UNASSIGNED: "UNASSIGNED",
  MESSAGE_ADDED: "MESSAGE_ADDED",
  ATTACHMENT_ADDED: "ATTACHMENT_ADDED",
  ATTACHMENT_REMOVED: "ATTACHMENT_REMOVED",
  ESCALATED: "ESCALATED",
  RESOLVED: "RESOLVED",
  CLOSED: "CLOSED",
  REOPENED: "REOPENED",
  UPDATED: "UPDATED",
  DELETED: "DELETED",
});

// ===== BULK OPERATION CONSTANTS =====
// For admin bulk operations

export const BULK_OPERATION = Object.freeze({
  ASSIGN: "assign",
  RESOLVE: "resolve",
  CLOSE: "close",
  DELETE: "delete",
  PRIORITY_CHANGE: "priority_change",
  STATUS_CHANGE: "status_change",
});

// ===== TIME PERIOD CONSTANTS =====
// For analytics and reporting

export const TIME_PERIOD = Object.freeze({
  TODAY: "today",
  WEEK: "week",
  MONTH: "month",
  QUARTER: "quarter",
  YEAR: "year",
  CUSTOM: "custom",
});

// ===== GROUP BY CONSTANTS =====
// For analytics grouping

export const GROUP_BY = Object.freeze({
  DAY: "day",
  WEEK: "week",
  MONTH: "month",
  PRIORITY: "priority",
  STATUS: "status",
  MODULE: "module",
  AGENT: "agent",
});

// ===== EXPORT FORMAT CONSTANTS =====
// For data export functionality

export const EXPORT_FORMAT = Object.freeze({
  CSV: "csv",
  EXCEL: "excel",
  PDF: "pdf",
});

// ===== CONFIG TYPE CONSTANTS =====
// For support configuration

export const CONFIG_TYPE = Object.freeze({
  STRING: "string",
  NUMBER: "number",
  BOOLEAN: "boolean",
  JSON: "json",
  ARRAY: "array",
});

// ===== SUPPORT TICKET FILE UPLOAD CONSTANTS =====
// Following recipe-ms pattern for file upload organization
// Proper folder structure with organization-based separation

export const SUPPORT_FILE_UPLOAD_CONSTANT = Object.freeze({
  TICKET_ATTACHMENT: {
    folder: "support_ticket_attachments",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_tickets/${ticketId ? `${ticketId}/` : ""}${fileName}`
        : `support_defaults/support_tickets/${ticketId ? `${ticketId}/` : ""}${fileName}`,
  },
  MESSAGE_ATTACHMENT: {
    folder: "support_message_attachments",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      messageId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_messages/${ticketId ? `${ticketId}/` : ""}${messageId ? `${messageId}/` : ""}${fileName}`
        : `support_defaults/support_messages/${ticketId ? `${ticketId}/` : ""}${messageId ? `${messageId}/` : ""}${fileName}`,
  },
  SUPPORT_DOCUMENT: {
    folder: "support_documents",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_documents/${ticketId ? `${ticketId}/` : ""}${fileName}`
        : `support_defaults/support_documents/${ticketId ? `${ticketId}/` : ""}${fileName}`,
  },
  SUPPORT_IMAGE: {
    folder: "support_images",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_images/${ticketId ? `${ticketId}/` : ""}${fileName}`
        : `support_defaults/support_images/${ticketId ? `${ticketId}/` : ""}${fileName}`,
  },
  SUPPORT_VIDEO: {
    folder: "support_videos",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_videos/${ticketId ? `${ticketId}/` : ""}${fileName}`
        : `support_defaults/support_videos/${ticketId ? `${ticketId}/` : ""}${fileName}`,
  },
  SUPPORT_AUDIO: {
    folder: "support_audio",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_audio/${ticketId ? `${ticketId}/` : ""}${fileName}`
        : `support_defaults/support_audio/${ticketId ? `${ticketId}/` : ""}${fileName}`,
  },
  SUPPORT_SCREENSHOT: {
    folder: "support_screenshots",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_screenshots/${ticketId ? `${ticketId}/` : ""}${fileName}`
        : `support_defaults/support_screenshots/${ticketId ? `${ticketId}/` : ""}${fileName}`,
  },
  SUPPORT_LOG: {
    folder: "support_logs",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_logs/${ticketId ? `${ticketId}/` : ""}${fileName}`
        : `support_defaults/support_logs/${ticketId ? `${ticketId}/` : ""}${fileName}`,
  },
  SUPPORT_MISC: {
    folder: "support_misc",
    destinationPath: (
      orgName: string | null,
      ticketId: any,
      fileName: string
    ) =>
      orgName
        ? `${orgName}/support_misc/${ticketId ? `${ticketId}/` : ""}${fileName}`
        : `support_defaults/support_misc/${ticketId ? `${ticketId}/` : ""}${fileName}`,
  },
});

// ===== SORT ORDER CONSTANTS =====
// For sorting functionality

export const SORT_ORDER = Object.freeze({
  ASC: "ASC",
  DESC: "DESC",
});

// ===== SORT BY CONSTANTS =====
// For sorting fields

export const SORT_BY = Object.freeze({
  ID: "id",
  CREATED_AT: "created_at",
  UPDATED_AT: "updated_at",
  TITLE: "title",
  STATUS: "status",
  PRIORITY: "priority",
  TYPE: "type",
  MODULE: "module",
  ASSIGNED_TO: "assigned_to",
  CREATED_BY: "created_by",
  RESOLVED_AT: "resolved_at",
  CLOSED_AT: "closed_at",
  DEADLINE: "deadline",
  LAST_ACTIVITY: "last_activity",
  RESPONSE_TIME: "response_time",
  RESOLUTION_TIME: "resolution_time",
});

// ===== RATE LIMITING CONSTANTS =====
// Following auth-ms and recipe-ms patterns

export const RATE_LIMIT = Object.freeze({
  MAX_REQUESTS: 100, // Max requests per window
  WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  FILE_UPLOAD_PER_HOUR: 50, // File uploads per hour
  STRICT_LIMIT: 5, // For sensitive operations
  STRICT_WINDOW_MS: 5 * 60 * 1000, // 5 minutes
});

// ===== RABBITMQ CONSTANTS =====
// Following recipe-ms and auth-ms patterns

export const RABBITMQ_QUEUE = Object.freeze({
  TICKET_CREATED: "ticket_created",
  TICKET_UPDATED: "ticket_updated",
  TICKET_ASSIGNED: "ticket_assigned",
  TICKET_RESOLVED: "ticket_resolved",
  TICKET_CLOSED: "ticket_closed",
  EMAIL_NOTIFICATION: "email_notification",
  PUSH_NOTIFICATION_SUCCESS: "push_notification_success",
  USER_ACTIVITY_LOG: "user_activity_log",
  // Mail queues following auth-ms pattern
  MAIL_FAILED: "mail_failed",
  MAIL_SUCCESS: "mail_success",
  // Support ticket specific queues
  SUPPORT_TICKET_EMAIL: "support_ticket_email",
  SUPPORT_NOTIFICATION: "support_notification",
  TICKET_STATUS_UPDATE: "ticket_status_update",
  TICKET_ASSIGNMENT_UPDATE: "ticket_assignment_update",
});

// ===== EMAIL CONSTANTS =====
// Following auth-ms patterns

export const EMAIL_CONSTANT = Object.freeze({
  TICKET_CREATED_SUBJECT: "Support Ticket Created",
  TICKET_UPDATED_SUBJECT: "Support Ticket Updated",
  TICKET_RESOLVED_SUBJECT: "Support Ticket Resolved",
  TICKET_CLOSED_SUBJECT: "Support Ticket Closed",
  FROM_EMAIL: "<EMAIL>",
});

// ===== RESPONSE CONSTANTS =====
// Following recipe-ms patterns with proper pagination and filtering

export const RESPONSE_CONSTANT = Object.freeze({
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  MAX_SEARCH_LENGTH: 100,
  MAX_EXPORT_LIMIT: 10000,
  DEFAULT_SORT_ORDER: "DESC",
  DEFAULT_SORT_BY: "created_at",
});

// ===== VALIDATION CONSTANTS =====
// Following recipe-ms pattern for validation limits and file types

export const VALIDATION_CONSTANT = Object.freeze({
  // File upload limits
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB (increased from 5MB)
  MAX_FILES_PER_UPLOAD: 10,
  MAX_TOTAL_ATTACHMENTS: 25,

  // Text field limits
  TICKET_TITLE_MIN: 5,
  TICKET_TITLE_MAX: 200,
  TICKET_DESCRIPTION_MIN: 10,
  TICKET_DESCRIPTION_MAX: 5000,
  MESSAGE_CONTENT_MIN: 1,
  MESSAGE_CONTENT_MAX: 2000,

  // Legacy field names for backward compatibility
  TICKET_TITLE_MIN_LENGTH: 5,
  TICKET_TITLE_MAX_LENGTH: 200,
  TICKET_DESCRIPTION_MIN_LENGTH: 10,
  TICKET_DESCRIPTION_MAX_LENGTH: 5000,
  RESOLUTION_NOTE_MAX_LENGTH: 2000,
  REVIEW_COMMENT_MAX_LENGTH: 1000,
  RATING_MIN: 1,
  RATING_MAX: 5,

  // Allowed file types
  ALLOWED_IMAGE_TYPES: [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "image/bmp",
    "image/tiff",
    "image/svg+xml",
  ],
  ALLOWED_DOCUMENT_TYPES: [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "text/plain",
    "text/csv",
    "application/json",
    "application/xml",
  ],
  ALLOWED_VIDEO_TYPES: [
    "video/mp4",
    "video/avi",
    "video/mov",
    "video/wmv",
    "video/webm",
    "video/mkv",
  ],
  ALLOWED_AUDIO_TYPES: [
    "audio/mp3",
    "audio/wav",
    "audio/ogg",
    "audio/m4a",
    "audio/aac",
    "audio/flac",
  ],
  ALLOWED_ARCHIVE_TYPES: [
    "application/zip",
    "application/x-rar-compressed",
    "application/x-7z-compressed",
    "application/x-tar",
    "application/gzip",
  ],

  // File name restrictions
  FILENAME_MAX_LENGTH: 255,
  FILENAME_MIN_LENGTH: 1,

  // Pagination defaults
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,

  // Search limits
  SEARCH_MIN_LENGTH: 2,
  SEARCH_MAX_LENGTH: 100,
});

// ===== ITEM CATEGORY CONSTANTS =====
// Following recipe-ms pattern for item categorization

export const ITEM_CATEGORY = Object.freeze({
  SUPPORT_ATTACHMENT: "support_attachment",
  SUPPORT_DOCUMENT: "support_document",
  SUPPORT_IMAGE: "support_image",
  SUPPORT_VIDEO: "support_video",
  SUPPORT_AUDIO: "support_audio",
  SUPPORT_SCREENSHOT: "support_screenshot",
  SUPPORT_LOG: "support_log",
  SUPPORT_MISC: "support_misc",
});

// ===== NOTIFICATION CONSTANTS =====
// For system notifications and alerts

export const NOTIFICATION_TYPE = Object.freeze({
  TICKET_CREATED: "ticket_created",
  TICKET_ASSIGNED: "ticket_assigned",
  TICKET_STATUS_CHANGED: "ticket_status_changed",
  TICKET_PRIORITY_CHANGED: "ticket_priority_changed",
  MESSAGE_ADDED: "message_added",
  TICKET_RESOLVED: "ticket_resolved",
  TICKET_CLOSED: "ticket_closed",
  TICKET_ESCALATED: "ticket_escalated",
  TICKET_OVERDUE: "ticket_overdue",
  REMINDER: "reminder",
});

// ===== PERMISSION CONSTANTS =====
// For role-based access control

export const PERMISSION = Object.freeze({
  VIEW_ALL_TICKETS: "view_all_tickets",
  VIEW_OWN_TICKETS: "view_own_tickets",
  CREATE_TICKET: "create_ticket",
  UPDATE_TICKET: "update_ticket",
  DELETE_TICKET: "delete_ticket",
  ASSIGN_TICKET: "assign_ticket",
  RESOLVE_TICKET: "resolve_ticket",
  CLOSE_TICKET: "close_ticket",
  ESCALATE_TICKET: "escalate_ticket",
  ADD_MESSAGE: "add_message",
  ADD_ATTACHMENT: "add_attachment",
  VIEW_ANALYTICS: "view_analytics",
  EXPORT_DATA: "export_data",
  MANAGE_CONFIG: "manage_config",
  ADMIN_ACCESS: "admin_access",
});

// ===== ERROR MESSAGES =====
// Centralized error messages for consistency

export const ERROR_MESSAGES = Object.freeze({
  VALIDATION_ERROR: "Validation failed",
  FILE_TOO_LARGE: "File size exceeds maximum limit",
  FILE_TYPE_NOT_ALLOWED: "File type not allowed",
  TOO_MANY_FILES: "Too many files uploaded",
  TICKET_NOT_FOUND: "Ticket not found",
  MESSAGE_NOT_FOUND: "Message not found",
  UNAUTHORIZED: "Unauthorized access",
  FORBIDDEN: "Access forbidden",
  INVALID_STATUS: "Invalid status transition",
  INVALID_PRIORITY: "Invalid priority level",
  DUPLICATE_TICKET: "Duplicate ticket detected",
  UPLOAD_FAILED: "File upload failed",
  HASH_GENERATION_FAILED: "Hash generation failed",
  BUCKET_CREATION_FAILED: "Bucket creation failed",
  FILE_MOVE_FAILED: "File move operation failed",
  FILE_DELETE_FAILED: "File deletion failed",
});

// ===== SUCCESS MESSAGES =====
// Centralized success messages for consistency

export const SUCCESS_MESSAGES = Object.freeze({
  TICKET_CREATED: "Ticket created successfully",
  TICKET_UPDATED: "Ticket updated successfully",
  TICKET_DELETED: "Ticket deleted successfully",
  TICKET_ASSIGNED: "Ticket assigned successfully",
  TICKET_RESOLVED: "Ticket resolved successfully",
  TICKET_CLOSED: "Ticket closed successfully",
  MESSAGE_ADDED: "Message added successfully",
  FILE_UPLOADED: "File uploaded successfully",
  FILE_DELETED: "File deleted successfully",
  BULK_OPERATION_COMPLETED: "Bulk operation completed successfully",
  EXPORT_COMPLETED: "Export completed successfully",
  CONFIG_UPDATED: "Configuration updated successfully",
});
